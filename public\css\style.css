hr {
  background-color: #f0f0f0;
}
p {
  font-size: 14px;
  color: #333333;
}
a {
  text-decoration: none;
  color: #333;
}
/*Header styles*/
.header-con {
  background-color: #ffd54c;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.header-top-con {
  align-items: center;
  display: flex;
  width: 100%;
  justify-content: center;
}
.header-top {
  display: flex;
  justify-content: space-between;
  width: 83%;
  border-bottom: 1px solid rgba(128, 128, 128, 0.164);
  padding: 12px 0;
}
.follow-us {
  display: flex;
  gap: 11px;
}
.icons {
  display: flex;
  gap: 7px;
}
.header-middle {
  display: flex;
  justify-content: space-between;
  width: 83%;
  align-items: center;
  margin: 25px 0px;
}

.logo img {
  width: 50px;
}
.search-con {
  display: flex;
  width: 540px;
  background: #fff;
  border-radius: 5px;
}
.search-con input {
  border: 0;
  background: none;
  width: 100%;
  height: 47px;
  color: #9e9e9e;
  font-size: 13px;
  font-weight: 400;
  padding: 0 80px 0 20px;
}
.search-btn button {
  background: #333;
  width: 60px;
  height: 100%;
  color: #fff;
  font-weight: 500;
  font-size: 25px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.header-bottom {
  background-color: white;
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 1rem 0;
  box-shadow: 0 3px 11px 0 rgba(0, 0, 0, 0.1);
}
.nav-con {
  display: flex;
  gap: 50px;
  width: 83%;
}

/*Hero section*/

.hero-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}
.hero-con {
  display: flex;
  width: 83%;
  gap: 20px;
}
.cat-row {
  width: 32%;
  border: 1px solid #ddd;
  height: 520px;
}
.cat-title {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: #333;
  justify-content: space-between;
  color: white;
  height: 55px;
  margin-bottom: 16px;
  font-weight: 600;
}
.cat-menu-title {
  display: flex;
  gap: 7px;
}
.cat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 15px;
  text-wrap: nowrap;
}
.hero-slide-con {
  background-color: #f1f1f1;
  width: 100%;
}
.hero-slide-text {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  justify-content: center;
  width: 50%;
  height: 100%;
  margin-left: 60px;
  gap: 12px;
}
.bg-txt {
  width: 300px;
  text-align: start;
  font-size: 48px;
  font-family: "Lato", sans-serif;
  color: #333333;
  margin-bottom: 20px;
}
.top-qual-con {
  display: flex;
  gap: 15px;
  align-items: center;
  font-size: 24px;
}
.hr-slide {
  width: 75px;
}
.browse-btn {
  background: #ffd54c;
  box-shadow: none;
  color: #ffffff;
  display: inline-block;
  height: 45px;
  line-height: 45px;
  padding: 0 22px;
  text-transform: uppercase;
  font-size: 13px;
  border-radius: 3px;
}
.browse-btn a {
  font-size: 16px;
  font-weight: 400;
  margin-top: 45px;
  color: #333;
  height: 48px;
  line-height: 47px;
  padding: 0 25px;
  font-family: "Lato", sans-serif;
  border-radius: 5px;
  text-wrap: nowrap;
}
/*Insert section*/
.insert-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 55px 0px;
}
.insert-con {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 83%;
  border: #ddd solid 1px;
  padding: 0px 31px;
  height: 115px;
  border-radius: 4px;
}

.insert-item {
  display: flex;
  gap: 15px;
}
.insert-icon {
  font-size: 41px;
  color: #ffd54c;
}
.insert-txt-con {
  display: flex;
  flex-direction: column;
  text-align: left;
}
.insert-txt-con h2 {
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 700;
}
.insert-txt-con p {
  font-size: 13px;
  line-height: 18px;
  padding-right: 5px;
}
.vl-insert {
  border-left: #ddd solid 1px;
  height: 72px;
  padding: 0 5px;
}

/*Product section*/
.product-area {
  margin-bottom: 50px;
}
.product-area-con {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 23px;
}
.product-title-con {
  width: 83%;
}
.product-tabs {
  display: flex;
  align-items: center;
}
.product-tabs-con {
  display: flex;
  align-items: center;
  border: 1px solid grey;
  border-radius: 100px;
  padding: 4px 57px;
  gap: 10px;
}
.product-tabs-con p {
  text-wrap: nowrap;
  font-size: 14px;
  font-weight: 700;
}
.hr-prod {
  width: 300px;
  margin-left: 30px;
}
.hr-tab {
  width: 15px;
}
.arrows-con {
  display: flex;
  border: 1px grey solid;
  border-radius: 33px;
  align-items: center;
  padding: 8px 8px;
  justify-content: space-between;
  height: 30px;
  gap: 5px;
}
.hr-arrows {
  width: 300px;
}
.product-carousel-con {
  display: flex;
  justify-content: center;
  align-items: center;
}
.carousel-con {
  width: 83%;
  display: flex;
  align-items: center;
  overflow: hidden;
}
.product-item-con {
  width: 100%;
  border: 1px solid #ddd;
  margin-right: 20px;
}
.product-item {
  padding: 20px;
}

.product-item-img-con img {
  width: 100%;
}
.span {
  padding: 3px;
  background: #ffd54c;
  width: fit-content;
  border-radius: 4px;
}
.product-item-img-con span {
  padding: 5px;
  background-color: #ffd54c;
  border-radius: 5px;
}
.product-item p {
  text-align: start;
  text-wrap: nowrap;
}
.ratings-con-prod {
  display: flex;
  gap: 1px;
  color: #ffcc00;
}
.ratings-con {
  color: #ffcc00;
}
/*Featured section*/

.featured-carousel-con {
  display: flex;
  flex-wrap: wrap;
}
.featured-item-con {
  padding: 35px;
  background-color: white;
  flex-grow: 1;
}
.featured-area {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f6f6f6;
  padding: 50px 0;
}
.featured-area-con {
  width: 83%;
}
.featured-slice {
  border-right: solid 1px #ddd;
}
.hr-feat {
  width: 100%;
  align-content: center;
}
.featured-title-con {
  display: flex;
  gap: 50px;
  margin-bottom: 30px;
}
.featured-item-content {
  display: flex;
  gap: 14px;
}
.featured-item-content img {
  height: 150px;
}
.featured-txt {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  justify-content: center;
  text-align: left;
}
.shop-now {
  margin: 15px 0;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-left: 20px;
}

/*Small product section*/
.small-prod-area {
  display: flex;
  justify-content: center;
  margin: 50px 0;
}
.small-prod-area-con {
  width: 83%;
  display: flex;
  flex-direction: column;
}
.small-prod-title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  margin-bottom: 30px;
}
.small-prod-item {
  width: 100%;
  padding: 18px;
  border: 1px solid #f0f0f0;
}
.small-prod-item-con {
  display: flex;
  align-items: center;
  gap: 50px;
}
.small-prod-txt {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  gap: 16px;
  width: 50%;
}
.small-prod-img-con img {
  height: 90px;
}
.hr-small-prod {
  width: 100%;
  align-content: center;
  padding-left: 55px;
}

/*Banner section*/
.banner-con {
  display: flex;
  width: 83%;
  justify-content: space-between;
}
.banner {
  display: flex;
  justify-content: center;
}
.banner-content {
  width: 49%;
  background-color: #f6f6f6;
  height: 220px;
}

/*Brands section*/
.brands {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50px;
}
.brands-con {
  display: flex;
  width: 83%;
  align-items: center;
  justify-content: space-evenly;
  border: 1px solid #f0f0f0;
  border-radius: 5px;
}
/*Custom section*/

.custom-area {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50px;
}
.custom-area-con {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 83%;
}
.custom-items-con {
  flex-grow: 1;
}

.action-area {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background-color: #ffd54c;
  padding: 45px 0;
}
.action-con {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
}
.action-item-con {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 50px;
  width: 83%;
}
.discover-btn button {
  line-height: 48px;
  background: #333;
  color: #fff;
  padding: 0 33px;
  display: inline-block;
  text-transform: uppercase;
  border-radius: 25px;
  -webkit-border-radius: 25px;
}
.action-icons {
  font-size: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}
/*Footer section*/

.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 35px 0;
}
.footer-con {
  display: flex;
  align-items: start;
  justify-content: space-between;
  width: 83%;
  text-align: start;
}
.info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.extras {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.newsletter {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 30%;
}
.subscribe-con {
  display: flex;
}
.subscribe-con input {
  height: 48px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: #f0f0f0;
  border: none;
  flex-grow: 1;
}
.subscribe-con button {
  background: #ffd54c;
  font-weight: 400;
  padding: 0 18px;
  border: 0;
  text-transform: uppercase;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

/*SHOP SECTION*/

.shop-area {
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop-space {
  display: flex;
  flex-wrap: wrap;
}

/*Contact section*/
.contact-area {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 55px;
  flex-direction: column;
}
.contact-area img {
  max-width: 100%;
  height: auto;
}
.contact-con {
  display: flex;
  justify-content: center;
  width: 83%;
  text-align: left;
  margin-top: 50px;
  gap: 10px;
}
.contact-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 50%;
}
.contact-details h1 {
  font-size: 21px;
  text-transform: capitalize;
  font-weight: 600;
  line-height: 20px;
  margin-bottom: 25px;
}

.contact-form {
  display: flex;
  flex-direction: column;
  width: 50%;
  gap: 20px;
}
.contact-form h1 {
  font-size: 21px;
  text-transform: capitalize;
  font-weight: 600;
  line-height: 20px;
  margin-bottom: 25px;
}
.contact-form form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.contact-form form input {
  border: 1px solid #f0f0f0;
  height: 45px;
  background: #ffffff;
  width: 100%;
  padding: 0 20px;
  color: #757575;
}
.contact-form form textarea {
  border: 1px solid #f0f0f0;
  height: 175px;
  background: #ffffff;
  width: 100%;
  padding: 0 20px;
  color: #757575;
}
.contact-form form button {
  font-weight: 400;
  height: 42px;
  line-height: 42px;
  padding: 0 30px;
  text-transform: capitalize;
  border: none;
  background: #333;
  color: #ffffff;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 4px;
  width: fit-content;
}
.contacts {
  display: flex;
  flex-direction: column;
  text-align: left;
  width: 50%;
}
.contact {
  display: flex;
  gap: 12px;
  padding: 20px 0;
}

/*Services Page*/
.page-title {
  width: 83%;
  padding: 50px 0;
  padding-top: 22px;
}
.services-area {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.services-con {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.cover-con {
  display: flex;
  gap: 35px;
  width: 83%;
  margin-bottom: 50px;
}
.cover-item {
  display: flex;
  flex-direction: column;
  text-align: left;
  align-items: center;
}
.cover-item img {
  width: 100%;
}
.cover-item p:nth-child(2) {
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 600;
  align-self: normal;
  padding: 8px 0;
  padding-top: 15px;
}

.service-items-con {
  display: flex;
  justify-content: center;
  width: 100%;
  background-color: #ececec;
}

.services {
  display: flex;
  flex-direction: column;
  width: 83%;
  align-items: center;
  gap: 30px;
  padding: 50px 0;
}
.segment {
  display: flex;
  text-align: left;
  gap: 10px;
}
.segment svg {
  color: #ffd54c;
  font-size: 42px;
}
.service-txt {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.service-txt h3 {
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 600;
}

.services-seg {
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.services-banner-con {
  display: flex;
  align-items: center;
  justify-content: center;
}
.sevices-banner {
  display: flex;
}
.sevices-banner img {
  width: 50%;
}
.services-banner-txt {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  gap: 20px;
}
.services-banner-txt h2 {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 25px;
}
.learn-btn {
  font-size: 15px;
  padding: 6px 20px;
  border: 1px solid #333;
  border-radius: 30px;
  background-color: white;
}

/*About Page*/
.about-section {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
.about-con {
  display: flex;
  flex-direction: column;
  width: 83%;
  justify-content: center;
  align-items: center;
}
.about-hero {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 20px;
}
.abt-hero-img img {
  max-width: 100%;
  height: auto;
}

/*Why Us section*/
.why-us-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 50px;
}
.why-us-con {
  display: flex;
  gap: 50px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.why-us-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}
.why-us-item svg {
  color: #ffd54c;
  font-size: 72px;
}
.feedback-area {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.testimo {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.user {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-bottom: 60px;
}
.customer-review {
  color: #ffd54c;
}

/*Undercarriage category*/

.undercarry {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.undercarry-con {
  display: flex;
  flex-direction: column;
  width: 83%;
  gap: 22px;
}
.undercarry-con img {
  max-width: 100%;
  height: auto;
}
.undercarry-con p {
  background: #f6f6f6;
  padding: 22px;
}
.undercarry-parts {
  display: flex;
  gap: 30px;
  margin-bottom: 50px;
}
.undercarry-item {
  border: 1px solid #3333;
}

/*slides*/
.swiper-slide {
  flex-shrink: 0;
  width: fit-content;
  height: auto;
  position: relative;
  transition-property: transform;
  display: block;
  margin-right: 0;
}
.brands-con-img {
  width: 90px;
}

/*Media Queries*/
.container {
  max-width: 1536px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* 2xl */
@media (max-width: 1536px) {
  .container {
    max-width: 1280px;
  }
}

/* xl */
@media (max-width: 1280px) {
  .container {
    max-width: 1024px;
  }
  .product-item p {
    font-size: 11.5px;
  }
  .featured-item-content img {
    height: 100px;
  }
  .featured-item-con {
    padding: 15px;
    background-color: white;
    min-height: 180px;
  }
}

/* lg */
@media (max-width: 1024px) {
  .container {
    max-width: 768px;
  }
  .insert-con {
    flex-wrap: wrap;
  }
  .insert-item {
    width: 50%;
  }
  .vl-insert {
    display: none;
  }
  .search-con {
    display: none;
  }
  .header-bottom {
    display: none;
  }
  .cat-row {
    display: none;
  }
  .custom-area-con {
    flex-wrap: wrap;
  }
  .action-item h1 {
    font-size: 15px;
  }
  .footer-con {
    flex-wrap: wrap;
  }
}

/* md */
@media (max-width: 768px) {
  .container {
    max-width: 640px;
  }
  .discover-btn button {
    line-height: 20px;
    padding: 0 16px;
  }

  .action-icons {
    gap: 10px;
    font-size: 15px;
  }
  .product-tabs-con {
    display: none;
  }
}

/* sm */
@media (max-width: 640px) {
  .container {
    max-width: 475px;
  }
  .subscribe-con {
    display: none;
  }
  .brands-con-img {
    width: 31px;
  }
}

/* xs */
@media (max-width: 475px) {
  .container {
    width: 100%;
  }
}
